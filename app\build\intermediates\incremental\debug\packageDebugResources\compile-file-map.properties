#Thu Jun 12 10:40:52 KST 2025
com.example.timemate.app-main-5\:/anim/fade_in.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
com.example.timemate.app-main-5\:/anim/fade_out.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_out.xml
com.example.timemate.app-main-5\:/animator/card_elevation_animator.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\animator\\card_elevation_animator.xml
com.example.timemate.app-main-5\:/color/bottom_nav_color.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_color.xml
com.example.timemate.app-main-5\:/color/category_text_selector.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\category_text_selector.xml
com.example.timemate.app-main-5\:/color/chip_text_selector.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\chip_text_selector.xml
com.example.timemate.app-main-5\:/color/ios_category_text_selector.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\ios_category_text_selector.xml
com.example.timemate.app-main-5\:/drawable/badge_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\badge_background.xml
com.example.timemate.app-main-5\:/drawable/bg_tag_rounded.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_tag_rounded.xml
com.example.timemate.app-main-5\:/drawable/bottom_sheet_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_sheet_background.xml
com.example.timemate.app-main-5\:/drawable/button_accept.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_accept.xml
com.example.timemate.app-main-5\:/drawable/button_outline.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_outline.xml
com.example.timemate.app-main-5\:/drawable/button_primary.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_primary.xml
com.example.timemate.app-main-5\:/drawable/button_primary_ios.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_primary_ios.xml
com.example.timemate.app-main-5\:/drawable/button_reject.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_reject.xml
com.example.timemate.app-main-5\:/drawable/button_secondary_ios.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_secondary_ios.xml
com.example.timemate.app-main-5\:/drawable/card_completed.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_completed.xml
com.example.timemate.app-main-5\:/drawable/card_normal.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_normal.xml
com.example.timemate.app-main-5\:/drawable/card_overdue.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_overdue.xml
com.example.timemate.app-main-5\:/drawable/card_selected.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_selected.xml
com.example.timemate.app-main-5\:/drawable/category_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\category_background.xml
com.example.timemate.app-main-5\:/drawable/category_button_selector.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\category_button_selector.xml
com.example.timemate.app-main-5\:/drawable/category_tag_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\category_tag_background.xml
com.example.timemate.app-main-5\:/drawable/circle_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background.xml
com.example.timemate.app-main-5\:/drawable/circle_background_ios.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background_ios.xml
com.example.timemate.app-main-5\:/drawable/circle_dot.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_dot.xml
com.example.timemate.app-main-5\:/drawable/edit_text_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edit_text_background.xml
com.example.timemate.app-main-5\:/drawable/header_gradient_blue.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\header_gradient_blue.xml
com.example.timemate.app-main-5\:/drawable/ic_access_time.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_access_time.xml
com.example.timemate.app-main-5\:/drawable/ic_add.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.example.timemate.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.example.timemate.app-main-5\:/drawable/ic_arrow_forward.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_forward.xml
com.example.timemate.app-main-5\:/drawable/ic_arrow_right.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_right.xml
com.example.timemate.app-main-5\:/drawable/ic_calendar.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_calendar.xml
com.example.timemate.app-main-5\:/drawable/ic_cancel.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cancel.xml
com.example.timemate.app-main-5\:/drawable/ic_check.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.example.timemate.app-main-5\:/drawable/ic_check_circle.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_circle.xml
com.example.timemate.app-main-5\:/drawable/ic_close.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.example.timemate.app-main-5\:/drawable/ic_directions.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_directions.xml
com.example.timemate.app-main-5\:/drawable/ic_directions_car.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_directions_car.xml
com.example.timemate.app-main-5\:/drawable/ic_directions_transit.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_directions_transit.xml
com.example.timemate.app-main-5\:/drawable/ic_directions_walk.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_directions_walk.xml
com.example.timemate.app-main-5\:/drawable/ic_driving.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_driving.xml
com.example.timemate.app-main-5\:/drawable/ic_edit.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit.xml
com.example.timemate.app-main-5\:/drawable/ic_friends.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_friends.xml
com.example.timemate.app-main-5\:/drawable/ic_home.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.example.timemate.app-main-5\:/drawable/ic_image_error.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image_error.xml
com.example.timemate.app-main-5\:/drawable/ic_image_placeholder.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image_placeholder.xml
com.example.timemate.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.timemate.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.timemate.app-main-5\:/drawable/ic_location.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location.xml
com.example.timemate.app-main-5\:/drawable/ic_location_end.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location_end.xml
com.example.timemate.app-main-5\:/drawable/ic_location_on.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location_on.xml
com.example.timemate.app-main-5\:/drawable/ic_location_start.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location_start.xml
com.example.timemate.app-main-5\:/drawable/ic_map_error.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_map_error.xml
com.example.timemate.app-main-5\:/drawable/ic_map_placeholder.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_map_placeholder.xml
com.example.timemate.app-main-5\:/drawable/ic_notifications.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notifications.xml
com.example.timemate.app-main-5\:/drawable/ic_person.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_person.xml
com.example.timemate.app-main-5\:/drawable/ic_phone.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_phone.xml
com.example.timemate.app-main-5\:/drawable/ic_profile.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_profile.xml
com.example.timemate.app-main-5\:/drawable/ic_route_bus.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_route_bus.xml
com.example.timemate.app-main-5\:/drawable/ic_route_car.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_route_car.xml
com.example.timemate.app-main-5\:/drawable/ic_route_walk.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_route_walk.xml
com.example.timemate.app-main-5\:/drawable/ic_schedule.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_schedule.xml
com.example.timemate.app-main-5\:/drawable/ic_schedule_notification.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_schedule_notification.xml
com.example.timemate.app-main-5\:/drawable/ic_snooze.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_snooze.xml
com.example.timemate.app-main-5\:/drawable/ic_time.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_time.xml
com.example.timemate.app-main-5\:/drawable/ic_transit.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_transit.xml
com.example.timemate.app-main-5\:/drawable/ic_walking.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_walking.xml
com.example.timemate.app-main-5\:/drawable/ic_weather.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_weather.xml
com.example.timemate.app-main-5\:/drawable/indicator_dot_active.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\indicator_dot_active.xml
com.example.timemate.app-main-5\:/drawable/indicator_dot_inactive.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\indicator_dot_inactive.xml
com.example.timemate.app-main-5\:/drawable/ios_badge_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_badge_background.xml
com.example.timemate.app-main-5\:/drawable/ios_button_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_button_background.xml
com.example.timemate.app-main-5\:/drawable/ios_card_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_card_background.xml
com.example.timemate.app-main-5\:/drawable/ios_category_button_selector.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_category_button_selector.xml
com.example.timemate.app-main-5\:/drawable/ios_chip_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_chip_background.xml
com.example.timemate.app-main-5\:/drawable/ios_circle_button_white.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_circle_button_white.xml
com.example.timemate.app-main-5\:/drawable/ios_count_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_count_background.xml
com.example.timemate.app-main-5\:/drawable/ios_distance_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_distance_background.xml
com.example.timemate.app-main-5\:/drawable/ios_header_blue_gradient.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_header_blue_gradient.xml
com.example.timemate.app-main-5\:/drawable/ios_header_gradient.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_header_gradient.xml
com.example.timemate.app-main-5\:/drawable/ios_icon_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_icon_background.xml
com.example.timemate.app-main-5\:/drawable/ios_image_placeholder.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_image_placeholder.xml
com.example.timemate.app-main-5\:/drawable/ios_navigation_button.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_navigation_button.xml
com.example.timemate.app-main-5\:/drawable/ios_rating_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_rating_background.xml
com.example.timemate.app-main-5\:/drawable/ios_search_button.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ios_search_button.xml
com.example.timemate.app-main-5\:/drawable/modal_handle.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\modal_handle.xml
com.example.timemate.app-main-5\:/drawable/recommended_badge.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\recommended_badge.xml
com.example.timemate.app-main-5\:/drawable/route_normal_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\route_normal_background.xml
com.example.timemate.app-main-5\:/drawable/route_recommended_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\route_recommended_background.xml
com.example.timemate.app-main-5\:/drawable/schedule_info_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\schedule_info_background.xml
com.example.timemate.app-main-5\:/drawable/status_badge_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_badge_background.xml
com.example.timemate.app-main-5\:/drawable/timemate_button_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\timemate_button_background.xml
com.example.timemate.app-main-5\:/drawable/timemate_button_outlined.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\timemate_button_outlined.xml
com.example.timemate.app-main-5\:/drawable/timemate_button_primary.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\timemate_button_primary.xml
com.example.timemate.app-main-5\:/drawable/timemate_edittext_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\timemate_edittext_background.xml
com.example.timemate.app-main-5\:/drawable/weather_card_background.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\weather_card_background.xml
com.example.timemate.app-main-5\:/font/pretendard_bold.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\pretendard_bold.xml
com.example.timemate.app-main-5\:/font/pretendard_medium.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\pretendard_medium.xml
com.example.timemate.app-main-5\:/font/pretendard_regular.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\pretendard_regular.xml
com.example.timemate.app-main-5\:/layout/activity_account_switch.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_account_switch.xml
com.example.timemate.app-main-5\:/layout/activity_friend_add.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_friend_add.xml
com.example.timemate.app-main-5\:/layout/activity_friend_list.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_friend_list.xml
com.example.timemate.app-main-5\:/layout/activity_home.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_home.xml
com.example.timemate.app-main-5\:/layout/activity_main.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.timemate.app-main-5\:/layout/activity_manual_login.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_manual_login.xml
com.example.timemate.app-main-5\:/layout/activity_notification.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_notification.xml
com.example.timemate.app-main-5\:/layout/activity_notifications.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_notifications.xml
com.example.timemate.app-main-5\:/layout/activity_password_reset.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_password_reset.xml
com.example.timemate.app-main-5\:/layout/activity_profile.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_profile.xml
com.example.timemate.app-main-5\:/layout/activity_recommendation.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_recommendation.xml
com.example.timemate.app-main-5\:/layout/activity_schedule_add.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_schedule_add.xml
com.example.timemate.app-main-5\:/layout/activity_schedule_calendar.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_schedule_calendar.xml
com.example.timemate.app-main-5\:/layout/activity_schedule_list.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_schedule_list.xml
com.example.timemate.app-main-5\:/layout/activity_schedule_reminder_detail.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_schedule_reminder_detail.xml
com.example.timemate.app-main-5\:/layout/activity_signup_form.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_signup_form.xml
com.example.timemate.app-main-5\:/layout/dialog_directions_bottom_sheet.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_directions_bottom_sheet.xml
com.example.timemate.app-main-5\:/layout/dialog_friend_selection.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_friend_selection.xml
com.example.timemate.app-main-5\:/layout/dialog_route_options.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_route_options.xml
com.example.timemate.app-main-5\:/layout/dialog_schedule_detail.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_schedule_detail.xml
com.example.timemate.app-main-5\:/layout/dialog_schedule_detail_improved.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_schedule_detail_improved.xml
com.example.timemate.app-main-5\:/layout/dialog_schedule_detail_ios.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_schedule_detail_ios.xml
com.example.timemate.app-main-5\:/layout/item_account.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_account.xml
com.example.timemate.app-main-5\:/layout/item_friend.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_friend.xml
com.example.timemate.app-main-5\:/layout/item_friend_selection.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_friend_selection.xml
com.example.timemate.app-main-5\:/layout/item_home_schedule.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_home_schedule.xml
com.example.timemate.app-main-5\:/layout/item_notification.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_notification.xml
com.example.timemate.app-main-5\:/layout/item_ootd_recommendation.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_ootd_recommendation.xml
com.example.timemate.app-main-5\:/layout/item_place_autocomplete.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_place_autocomplete.xml
com.example.timemate.app-main-5\:/layout/item_place_suggest.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_place_suggest.xml
com.example.timemate.app-main-5\:/layout/item_place_suggestion.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_place_suggestion.xml
com.example.timemate.app-main-5\:/layout/item_place_with_image.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_place_with_image.xml
com.example.timemate.app-main-5\:/layout/item_recommendation.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_recommendation.xml
com.example.timemate.app-main-5\:/layout/item_route_card.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_route_card.xml
com.example.timemate.app-main-5\:/layout/item_route_option.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_route_option.xml
com.example.timemate.app-main-5\:/layout/item_schedule.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_schedule.xml
com.example.timemate.app-main-5\:/layout/item_schedule_detail.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_schedule_detail.xml
com.example.timemate.app-main-5\:/layout/item_schedule_header.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_schedule_header.xml
com.example.timemate.app-main-5\:/layout/item_schedule_improved.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_schedule_improved.xml
com.example.timemate.app-main-5\:/layout/item_schedule_list.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_schedule_list.xml
com.example.timemate.app-main-5\:/layout/item_today_schedule.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_today_schedule.xml
com.example.timemate.app-main-5\:/layout/item_tomorrow_reminder_card.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_tomorrow_reminder_card.xml
com.example.timemate.app-main-5\:/layout/item_tomorrow_reminder_header.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_tomorrow_reminder_header.xml
com.example.timemate.app-main-5\:/menu/bottom_nav_menu.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
com.example.timemate.app-main-5\:/mipmap-anydpi/ic_launcher.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.example.timemate.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.example.timemate.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.timemate.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.timemate.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.timemate.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.timemate.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.timemate.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.timemate.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.timemate.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.timemate.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.timemate.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.timemate.app-main-5\:/xml/backup_rules.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.timemate.app-main-5\:/xml/data_extraction_rules.xml=C\:\\Users\\samg2\\TimeMate\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
